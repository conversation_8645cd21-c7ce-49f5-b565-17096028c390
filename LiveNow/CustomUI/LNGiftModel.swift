//
//  LNGiftModel.swift
//  LiveNow
//
//  Created by AI on 2025/8/23.
//

import UIKit

/// 礼物数据模型
class LNGiftModel {
    /// 礼物ID
    var giftId: String
    /// 礼物名称
    var name: String
    /// 礼物图片URL或本地图片名
    var imageUrl: String
    /// 礼物价格（钻石数量）
    var price: Int
    /// 是否为特殊礼物
    var isSpecial: Bool
    /// 礼物类型
    var type: LNGiftType
    
    init(giftId: String, name: String, imageUrl: String, price: Int, isSpecial: Bool = false, type: LNGiftType = .normal) {
        self.giftId = giftId
        self.name = name
        self.imageUrl = imageUrl
        self.price = price
        self.isSpecial = isSpecial
        self.type = type
    }
}

/// 礼物类型枚举
enum LNGiftType {
    case normal     // 普通礼物
    case special    // 特殊礼物
    case vip        // VIP专属礼物
}

/// 礼物管理器 - 提供礼物数据
class LNGiftManager {
    static let shared = LNGiftManager()
    
    private init() {}
    
    
    func getGiftList() {
        
        NetWorkRequest(LNApiProfile.giftList(par: [:])) { result in
            
        } failure: { error in
            
        }
    }
    
    
    /// 获取所有礼物数据
    func getAllGifts() -> [LNGiftModel] {
        return [
            // 第一页礼物 (8个)
            LNGiftModel(giftId: "1", name: "彩带", imageUrl: "gift_ribbon", price: 100),
            LNGiftModel(giftId: "2", name: "花束", imageUrl: "gift_flowers", price: 100),
            LNGiftModel(giftId: "3", name: "彩带", imageUrl: "gift_ribbon", price: 100),
            LNGiftModel(giftId: "4", name: "花束", imageUrl: "gift_flowers", price: 100),
            LNGiftModel(giftId: "5", name: "跑车", imageUrl: "gift_car", price: 100, isSpecial: true),
            LNGiftModel(giftId: "6", name: "彩带", imageUrl: "gift_ribbon", price: 100),
            LNGiftModel(giftId: "7", name: "跑车", imageUrl: "gift_car", price: 100, isSpecial: true),
            LNGiftModel(giftId: "8", name: "彩带", imageUrl: "gift_ribbon", price: 100),
            
            // 第二页礼物 (8个)
            LNGiftModel(giftId: "9", name: "钻石", imageUrl: "gift_diamond", price: 200),
            LNGiftModel(giftId: "10", name: "皇冠", imageUrl: "gift_crown", price: 500),
            LNGiftModel(giftId: "11", name: "火箭", imageUrl: "gift_rocket", price: 1000, type: .vip),
            LNGiftModel(giftId: "12", name: "城堡", imageUrl: "gift_castle", price: 2000, type: .vip),
            LNGiftModel(giftId: "13", name: "钻石", imageUrl: "gift_diamond", price: 200),
            LNGiftModel(giftId: "14", name: "皇冠", imageUrl: "gift_crown", price: 500),
            LNGiftModel(giftId: "15", name: "火箭", imageUrl: "gift_rocket", price: 1000, type: .vip),
            LNGiftModel(giftId: "16", name: "城堡", imageUrl: "gift_castle", price: 2000, type: .vip),
            
            // 第三页礼物 (8个)
            LNGiftModel(giftId: "17", name: "玫瑰", imageUrl: "gift_rose", price: 50),
            LNGiftModel(giftId: "18", name: "巧克力", imageUrl: "gift_chocolate", price: 80),
            LNGiftModel(giftId: "19", name: "香水", imageUrl: "gift_perfume", price: 150),
            LNGiftModel(giftId: "20", name: "戒指", imageUrl: "gift_ring", price: 300),
            LNGiftModel(giftId: "21", name: "玫瑰", imageUrl: "gift_rose", price: 50),
            LNGiftModel(giftId: "22", name: "巧克力", imageUrl: "gift_chocolate", price: 80),
            LNGiftModel(giftId: "23", name: "香水", imageUrl: "gift_perfume", price: 150),
            LNGiftModel(giftId: "24", name: "戒指", imageUrl: "gift_ring", price: 300)
        ]
    }
    
    /// 按页面分组获取礼物数据
    func getGiftsByPages() -> [[LNGiftModel]] {
        let allGifts = getAllGifts()
        let pageSize = 8 // 每页8个礼物 (2行 x 4列)
        
        var pages: [[LNGiftModel]] = []
        for i in stride(from: 0, to: allGifts.count, by: pageSize) {
            let endIndex = min(i + pageSize, allGifts.count)
            let pageGifts = Array(allGifts[i..<endIndex])
            pages.append(pageGifts)
        }
        
        return pages
    }
}
