//
//  LNRechargeViewController.swift
//  LiveNow
//
//  Created by Augment Agent on 2025/08/17.
//

import UIKit
import SnapKit

/// 充值页面（Recharge）
/// - 顶部显示当前钻石数量
/// - 充值选项列表，支持折扣标签
/// - 使用渐变背景和卡片式设计
class LNRechargeViewController: LNBaseController {

    // 使用透明导航栏
    override var navigationSolidColor: UIColor { return .clear }
    override var navigationTitleColor: UIColor { return .black }
    // 使用圆弧形渐变背景
    override var useArcGradientBackground: Bool { return true }
    
    // MARK: - Model
    struct RechargeOption {
        let diamonds: Int
        let bonusDiamonds: Int
        let priceUSD: String
        let discountLabel: String?
        
        var totalDiamonds: Int { return diamonds + bonusDiamonds }
    }
    
    private var currentDiamonds: Int = 0
    
    private let rechargeOptions: [RechargeOption] = [
        RechargeOption(diamonds: 199, bonusDiamonds: 100, priceUSD: "US$4.99", discountLabel: nil),
        RechargeOption(diamonds: 199, bonusDiamonds: 100, priceUSD: "US$4.99", discountLabel: "30%Off"),
        RechargeOption(diamonds: 199, bonusDiamonds: 100, priceUSD: "US$4.99", discountLabel: "50%Off"),
        RechargeOption(diamonds: 199, bonusDiamonds: 100, priceUSD: "US$4.99", discountLabel: nil),
        RechargeOption(diamonds: 199, bonusDiamonds: 100, priceUSD: "US$4.99", discountLabel: nil),
        RechargeOption(diamonds: 199, bonusDiamonds: 100, priceUSD: "US$4.99", discountLabel: nil)
    ]
    
    // MARK: - UI Components
    private lazy var scrollView: UIScrollView = {
        let sv = UIScrollView()
        sv.backgroundColor = .clear
        sv.showsVerticalScrollIndicator = false
        return sv
    }()
    
    private lazy var contentView: UIView = {
        let v = UIView()
        v.backgroundColor = .clear
        return v
    }()
    
    private lazy var diamondHeaderView: UIView = {
        let v = UIView()
        v.backgroundColor = .clear
        
        // 创建渐变背景
        let gradientLayer = CAGradientLayer()
        gradientLayer.colors = [
            UIColor.hex(hexString: "#B8FFE6").cgColor,
            UIColor.hex(hexString: "#E2FFD9").cgColor
        ]
        gradientLayer.startPoint = CGPoint(x: 0, y: 0)
        gradientLayer.endPoint = CGPoint(x: 1, y: 0)
        gradientLayer.cornerRadius = s(16)
        v.layer.insertSublayer(gradientLayer, at: 0)
        
        // 在layoutSubviews中更新gradientLayer的frame
        v.tag = 1001
        
        return v
    }()
    
    private lazy var diamondTitleLabel: UILabel = {
        let l = UILabel()
        l.text = "Available Diamonds"
        l.font = LNFont.medium(16)
        l.textColor = UIColor.hex(hexString: "#333333")
        return l
    }()
    
    private lazy var diamondCountLabel: UILabel = {
        let l = UILabel()
        l.text = "\(currentDiamonds)"
        l.font = LNFont.bold(48)
        l.textColor = UIColor.hex(hexString: "#333333")
        return l
    }()
    
    private lazy var diamondIconView: UIImageView = {
        let iv = UIImageView()
        iv.image = UIImage(named: "ic_diamond")
        iv.contentMode = .scaleAspectFit
        return iv
    }()
    
    private lazy var selectTitleLabel: UILabel = {
        let l = UILabel()
        l.text = "Please Select The Recharge Amount"
        l.font = LNFont.medium(16)
        l.textColor = UIColor.hex(hexString: "#333333")
        return l
    }()
    
    private lazy var optionsStackView: UIStackView = {
        let sv = UIStackView()
        sv.axis = .vertical
        sv.spacing = s(12)
        sv.distribution = .fill
        return sv
    }()
    
    override func viewDidLoad() {
        super.viewDidLoad()
        title = "Recharge"
        edgesForExtendedLayout = .all
        
        setupUI()
        loadCurrentDiamonds()
    }
    
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        
        // 更新钻石头部视图的渐变层
        if let gradientLayer = diamondHeaderView.layer.sublayers?.first as? CAGradientLayer {
            gradientLayer.frame = diamondHeaderView.bounds
        }
    }
    
    private func setupUI() {
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        
        contentView.addSubview(diamondHeaderView)
        diamondHeaderView.addSubview(diamondTitleLabel)
        diamondHeaderView.addSubview(diamondCountLabel)
        diamondHeaderView.addSubview(diamondIconView)
        
        contentView.addSubview(selectTitleLabel)
        contentView.addSubview(optionsStackView)
        
        setupConstraints()
        setupRechargeOptions()
    }
    
    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
        
        diamondHeaderView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(s(20))
            make.left.right.equalToSuperview().inset(s(16))
            make.height.equalTo(s(130))
        }
        
        diamondTitleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(s(20))
            make.left.equalToSuperview().offset(s(20))
        }
        
        diamondCountLabel.snp.makeConstraints { make in
            make.top.equalTo(diamondTitleLabel.snp.bottom).offset(s(8))
            make.left.equalTo(diamondTitleLabel)
            make.bottom.equalToSuperview().offset(-s(20))
        }
        
        diamondIconView.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-s(20))
            make.centerY.equalToSuperview()
            make.width.height.equalTo(s(60))
        }
        
        selectTitleLabel.snp.makeConstraints { make in
            make.top.equalTo(diamondHeaderView.snp.bottom).offset(s(32))
            make.left.equalToSuperview().offset(s(16))
        }
        
        optionsStackView.snp.makeConstraints { make in
            make.top.equalTo(selectTitleLabel.snp.bottom).offset(s(16))
            make.left.right.equalToSuperview().inset(s(16))
            make.bottom.equalToSuperview().offset(-s(20))
        }
    }

    private func setupRechargeOptions() {
        for option in rechargeOptions {
            let optionView = LNRechargeOptionView()
            let rechargeOption = LNRechargeOptionView.Option(
                diamonds: option.diamonds,
                bonusDiamonds: option.bonusDiamonds,
                priceUSD: option.priceUSD,
                discountLabel: option.discountLabel
            )
            optionView.configure(with: rechargeOption)
            optionView.onTapped = { [weak self] option in
                let rechargeOption = RechargeOption(
                    diamonds: option.diamonds,
                    bonusDiamonds: option.bonusDiamonds,
                    priceUSD: option.priceUSD,
                    discountLabel: option.discountLabel
                )
                self?.handleRecharge(option: rechargeOption)
            }
            optionsStackView.addArrangedSubview(optionView)
        }
    }

    private func handleRecharge(option: RechargeOption) {
        // 显示确认弹窗
        let alert = UIAlertController(
            title: "Confirm Purchase",
            message: "Purchase \(option.diamonds) + \(option.bonusDiamonds) diamonds for \(option.priceUSD)?",
            preferredStyle: .alert
        )

        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
        alert.addAction(UIAlertAction(title: "Purchase", style: .default) { [weak self] _ in
            self?.processPurchase(option: option)
        })

        present(alert, animated: true)
    }

    open func processPurchase(option: RechargeOption) {
        // 这里应该调用支付SDK进行实际支付
        // 目前只是模拟支付成功

        // 显示加载状态
        let loadingAlert = UIAlertController(title: "Processing...", message: nil, preferredStyle: .alert)
        present(loadingAlert, animated: true)

        // 模拟支付处理
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) { [weak self] in
            loadingAlert.dismiss(animated: true) {
                // 支付成功，更新钻石数量
                self?.currentDiamonds += option.totalDiamonds
                self?.diamondCountLabel.text = "\(self?.currentDiamonds ?? 0)"

                // 显示成功提示
                let successAlert = UIAlertController(
                    title: "Purchase Successful!",
                    message: "You have received \(option.totalDiamonds) diamonds.",
                    preferredStyle: .alert
                )
                successAlert.addAction(UIAlertAction(title: "OK", style: .default))
                self?.present(successAlert, animated: true)
            }
        }
    }

    private func loadCurrentDiamonds() {
        // 这里应该从服务器或本地存储加载当前钻石数量
        // 目前使用模拟数据
        currentDiamonds = 0
        diamondCountLabel.text = "\(currentDiamonds)"
    }
}
